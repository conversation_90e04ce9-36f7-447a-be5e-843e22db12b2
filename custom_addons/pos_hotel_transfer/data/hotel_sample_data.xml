<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Hotel Amenities -->
        <record id="amenity_wifi" model="hotel.amenity">
            <field name="name">WiFi</field>
            <field name="description">Free wireless internet</field>
        </record>

        <record id="amenity_ac" model="hotel.amenity">
            <field name="name">Air Conditioning</field>
            <field name="description">Climate control</field>
        </record>

        <!-- Hotel Services -->
        <record id="service_breakfast" model="hotel.service">
            <field name="name">Breakfast</field>
            <field name="category">food</field>
            <field name="price">25.00</field>
            <field name="description">Continental breakfast</field>
        </record>

        <record id="service_laundry" model="hotel.service">
            <field name="name">Laundry Service</field>
            <field name="category">service</field>
            <field name="price">15.00</field>
            <field name="description">Same day laundry</field>
        </record>

        <!-- Hotel Guests -->
        <record id="guest_john_smith" model="hotel.guest">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0123</field>
            <field name="nationality">American</field>
            <field name="id_type">passport</field>
            <field name="id_number">P123456789</field>
            <field name="address">123 Main St, New York, NY 10001</field>
        </record>

        <record id="guest_sarah_johnson" model="hotel.guest">
            <field name="name">Sarah Johnson</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0456</field>
            <field name="nationality">Canadian</field>
            <field name="id_type">national_id</field>
            <field name="id_number">ID987654321</field>
            <field name="address">456 Oak Ave, Toronto, ON M5V 3A8</field>
        </record>

        <record id="guest_michael_brown" model="hotel.guest">
            <field name="name">Michael Brown</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0789</field>
            <field name="nationality">British</field>
            <field name="id_type">passport</field>
            <field name="id_number">P555666777</field>
            <field name="address">789 Queen St, London, UK SW1A 1AA</field>
        </record>

        <record id="guest_emma_davis" model="hotel.guest">
            <field name="name">Emma Davis</field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0321</field>
            <field name="nationality">Australian</field>
            <field name="id_type">passport</field>
            <field name="id_number">P888999000</field>
            <field name="address">321 Collins St, Melbourne, VIC 3000</field>
        </record>

        <!-- Hotel Rooms -->
        <record id="room_101" model="hotel.room">
            <field name="name">101</field>
            <field name="room_type">single</field>
            <field name="floor">1</field>
            <field name="capacity">1</field>
            <field name="price_per_night">120.00</field>
            <field name="state">occupied</field>
        </record>

        <record id="room_102" model="hotel.room">
            <field name="name">102</field>
            <field name="room_type">single</field>
            <field name="floor">1</field>
            <field name="capacity">1</field>
            <field name="price_per_night">120.00</field>
            <field name="state">available</field>
        </record>

        <record id="room_201" model="hotel.room">
            <field name="name">201</field>
            <field name="room_type">double</field>
            <field name="floor">2</field>
            <field name="capacity">2</field>
            <field name="price_per_night">180.00</field>
            <field name="state">occupied</field>
        </record>

        <record id="room_202" model="hotel.room">
            <field name="name">202</field>
            <field name="room_type">double</field>
            <field name="floor">2</field>
            <field name="capacity">2</field>
            <field name="price_per_night">180.00</field>
            <field name="state">available</field>
        </record>

        <record id="room_301" model="hotel.room">
            <field name="name">301</field>
            <field name="room_type">suite</field>
            <field name="floor">3</field>
            <field name="capacity">4</field>
            <field name="price_per_night">350.00</field>
            <field name="state">occupied</field>
        </record>

        <record id="room_302" model="hotel.room">
            <field name="name">302</field>
            <field name="room_type">suite</field>
            <field name="floor">3</field>
            <field name="capacity">4</field>
            <field name="price_per_night">350.00</field>
            <field name="state">available</field>
        </record>

        <!-- Room Bookings -->
        <record id="booking_john_101" model="room.booking">
            <field name="name">BOOK001</field>
            <field name="guest_id" ref="guest_john_smith"/>
            <field name="room_id" ref="room_101"/>
            <field name="check_in" eval="(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(datetime.now() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">checked_in</field>
            <field name="adults">1</field>
            <field name="children">0</field>
        </record>

        <record id="booking_sarah_201" model="room.booking">
            <field name="name">BOOK002</field>
            <field name="guest_id" ref="guest_sarah_johnson"/>
            <field name="room_id" ref="room_201"/>
            <field name="check_in" eval="datetime.now().strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(datetime.now() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">confirmed</field>
            <field name="adults">2</field>
            <field name="children">1</field>
        </record>

        <record id="booking_michael_301" model="room.booking">
            <field name="name">BOOK003</field>
            <field name="guest_id" ref="guest_michael_brown"/>
            <field name="room_id" ref="room_301"/>
            <field name="check_in" eval="(datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(datetime.now() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">confirmed</field>
            <field name="adults">2</field>
            <field name="children">2</field>
        </record>

        <record id="booking_emma_102" model="room.booking">
            <field name="name">BOOK004</field>
            <field name="guest_id" ref="guest_emma_davis"/>
            <field name="room_id" ref="room_102"/>
            <field name="check_in" eval="datetime.now().strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(datetime.now() + timedelta(days=4)).strftime('%Y-%m-%d')"/>
            <field name="state">checked_in</field>
            <field name="adults">1</field>
            <field name="children">0</field>
        </record>
    </data>
</odoo>
