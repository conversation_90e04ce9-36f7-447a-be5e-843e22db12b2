<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Demo Hotel Guests -->
        <record id="demo_guest_1" model="hotel.guest">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0123</field>
            <field name="id_type">passport</field>
            <field name="id_number">P123456789</field>
        </record>

        <record id="demo_guest_2" model="hotel.guest">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0456</field>
            <field name="id_type">national_id</field>
            <field name="id_number">ID987654321</field>
        </record>

        <record id="demo_guest_3" model="hotel.guest">
            <field name="name"><PERSON></field>
            <field name="email"><EMAIL></field>
            <field name="phone">******-0789</field>
            <field name="id_type">passport</field>
            <field name="id_number">P555666777</field>
        </record>

        <!-- Demo Hotel Rooms -->
        <record id="demo_room_101" model="hotel.room">
            <field name="name">101</field>
            <field name="room_type">single</field>
            <field name="floor">1</field>
            <field name="capacity">1</field>
            <field name="price_per_night">120.00</field>
            <field name="state">occupied</field>
        </record>

        <record id="demo_room_201" model="hotel.room">
            <field name="name">201</field>
            <field name="room_type">double</field>
            <field name="floor">2</field>
            <field name="capacity">2</field>
            <field name="price_per_night">180.00</field>
            <field name="state">occupied</field>
        </record>

        <record id="demo_room_301" model="hotel.room">
            <field name="name">301</field>
            <field name="room_type">suite</field>
            <field name="floor">3</field>
            <field name="capacity">4</field>
            <field name="price_per_night">350.00</field>
            <field name="state">occupied</field>
        </record>

        <!-- Demo Room Bookings -->
        <record id="demo_booking_1" model="room.booking">
            <field name="name">BOOK001</field>
            <field name="guest_id" ref="demo_guest_1"/>
            <field name="room_id" ref="demo_room_101"/>
            <field name="check_in" eval="(DateTime.today() - timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(DateTime.today() + timedelta(days=2)).strftime('%Y-%m-%d')"/>
            <field name="state">checked_in</field>
            <field name="adults">1</field>
            <field name="children">0</field>
        </record>

        <record id="demo_booking_2" model="room.booking">
            <field name="name">BOOK002</field>
            <field name="guest_id" ref="demo_guest_2"/>
            <field name="room_id" ref="demo_room_201"/>
            <field name="check_in" eval="DateTime.today().strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(DateTime.today() + timedelta(days=3)).strftime('%Y-%m-%d')"/>
            <field name="state">confirmed</field>
            <field name="adults">2</field>
            <field name="children">1</field>
        </record>

        <record id="demo_booking_3" model="room.booking">
            <field name="name">BOOK003</field>
            <field name="guest_id" ref="demo_guest_3"/>
            <field name="room_id" ref="demo_room_301"/>
            <field name="check_in" eval="(DateTime.today() + timedelta(days=1)).strftime('%Y-%m-%d')"/>
            <field name="check_out" eval="(DateTime.today() + timedelta(days=5)).strftime('%Y-%m-%d')"/>
            <field name="state">confirmed</field>
            <field name="adults">2</field>
            <field name="children">2</field>
        </record>
    </data>
</odoo>
