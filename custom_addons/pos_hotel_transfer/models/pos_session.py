# -*- coding: utf-8 -*-
from odoo import models, api


class PosSession(models.Model):
    _inherit = 'pos.session'

    @api.model
    def get_hotel_reservations(self):
        """Get active hotel reservations for POS transfer"""
        reservations = self.env['room.booking'].search([
            ('state', 'in', ['confirmed', 'checked_in']),
        ])

        reservation_data = []
        for reservation in reservations:
            reservation_data.append({
                'id': reservation.id,
                'name': reservation.name,
                'guest_name': reservation.guest_id.name,
                'room_number': reservation.room_id.name,
                'room_type': dict(reservation.room_id._fields['room_type'].selection).get(reservation.room_id.room_type),
                'check_in': reservation.check_in.strftime('%Y-%m-%d') if reservation.check_in else '',
                'check_out': reservation.check_out.strftime('%Y-%m-%d') if reservation.check_out else '',
                'total_amount': reservation.total_amount,
                'due_amount': reservation.due_amount,
                'balance_amount': reservation.balance_amount,
                'state': reservation.state,
                'state_label': dict(reservation._fields['state'].selection).get(reservation.state),
                'adults': reservation.adults,
                'children': reservation.children,
            })

        return reservation_data

    @api.model
    def transfer_to_hotel_reservation(self, reservation_id, amount, order_ref=None):
        """Transfer POS order amount to hotel reservation as due amount"""
        try:
            reservation = self.env['room.booking'].browse(reservation_id)
            if not reservation.exists():
                return {'success': False, 'message': 'Reservation not found'}

            # Add the amount to the reservation's due amount
            result = reservation.add_pos_transfer(amount, order_ref)

            if result:
                return {
                    'success': True,
                    'message': f'Successfully transferred ${amount:.2f} to {reservation.name}',
                    'reservation_name': reservation.name,
                    'guest_name': reservation.guest_id.name,
                    'new_due_amount': reservation.due_amount,
                    'new_balance': reservation.balance_amount
                }
            else:
                return {'success': False, 'message': 'Transfer failed'}

        except Exception as e:
            return {'success': False, 'message': f'Error: {str(e)}'}
