/* Hotel Reservations Dialog Styles */
.modal-dialog .dialog-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-dialog .reservations-container {
    min-height: 300px;
}

.modal-dialog .reservation-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.modal-dialog .reservation-row:hover {
    background-color: #f8f9fa !important;
}

.modal-dialog .table th {
    font-weight: 600;
    color: #495057;
    border-top: none;
    font-size: 0.9rem;
}

.modal-dialog .table td {
    vertical-align: middle;
    font-size: 0.9rem;
}

.modal-dialog .selected-reservation {
    border: 2px solid #0d6efd;
    background-color: #f0f8ff !important;
}

.modal-dialog .btn {
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.375rem;
}

.modal-dialog .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.modal-dialog .badge {
    font-size: 0.8rem;
    padding: 0.35em 0.65em;
}

.modal-dialog .alert {
    margin-bottom: 1rem;
}

.modal-dialog .fa-spinner {
    color: #0d6efd;
}

.modal-dialog .table-responsive {
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
}

.modal-dialog .reservation-header {
    margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .modal-dialog .table {
        font-size: 0.8rem;
    }

    .modal-dialog .dialog-body {
        padding: 1rem;
    }
}
