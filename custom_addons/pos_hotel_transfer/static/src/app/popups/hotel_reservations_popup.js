/** @odoo-module */

import { Dialog } from "@web/core/dialog/dialog";
import { usePos } from "@point_of_sale/app/store/pos_hook";
import { useState, onMounted, Component } from "@odoo/owl";

export class HotelReservationsPopup extends Component {
    static template = "pos_hotel_transfer.HotelReservationsPopup";
    static components = { Dialog };
    static props = {
        close: Function,
        getPayload: { type: Function, optional: true },
    };

    setup() {
        this.pos = usePos();
        this.state = useState({
            reservations: [],
            selectedReservation: null,
            loading: true,
            error: null,
        });

        onMounted(async () => {
            await this.loadReservations();
        });
    }

    async loadReservations() {
        try {
            this.state.loading = true;
            this.state.error = null;

            const reservations = await this.env.services.orm.call(
                'pos.session',
                'get_hotel_reservations',
                []
            );

            this.state.reservations = reservations;
            this.state.loading = false;
        } catch (error) {
            console.error('Error loading hotel reservations:', error);
            this.state.error = 'Failed to load hotel reservations';
            this.state.loading = false;
        }
    }

    selectReservation(reservation) {
        this.state.selectedReservation = reservation;
    }

    async transferToReservation() {
        if (!this.state.selectedReservation) {
            this.env.services.notification.add(
                "Please select a reservation first",
                { type: "warning" }
            );
            return;
        }

        const currentOrder = this.pos.get_order();
        if (!currentOrder || currentOrder.get_orderlines().length === 0) {
            this.env.services.notification.add(
                "No items in the current order to transfer",
                { type: "warning" }
            );
            return;
        }

        try {
            const reservation = this.state.selectedReservation;
            const orderTotal = currentOrder.get_total_with_tax();
            const orderRef = currentOrder.name || `POS-${Date.now()}`;

            // Call backend to transfer amount to reservation
            const result = await this.env.services.orm.call(
                'pos.session',
                'transfer_to_hotel_reservation',
                [reservation.id, orderTotal, orderRef]
            );

            if (result.success) {
                // Add a note to the order about the transfer
                currentOrder.set_note(
                    `Transferred to Hotel - Room: ${reservation.room_number}, Guest: ${reservation.guest_name} (${orderRef})`
                );

                // Show success notification with details
                this.env.services.notification.add(
                    `✅ ${result.message}\nNew Due Amount: $${result.new_due_amount.toFixed(2)}\nBalance: $${result.new_balance.toFixed(2)}`,
                    {
                        type: "success",
                        sticky: false
                    }
                );

                this.confirm();
            } else {
                this.env.services.notification.add(
                    `❌ Transfer Failed: ${result.message}`,
                    { type: "danger" }
                );
            }
        } catch (error) {
            console.error('Error transferring to hotel:', error);
            this.env.services.notification.add(
                "❌ Failed to transfer order to hotel: " + error.message,
                { type: "danger" }
            );
        }
    }

    confirm() {
        if (this.props.getPayload) {
            this.props.getPayload({
                confirmed: true,
                selectedReservation: this.state.selectedReservation,
            });
        }
        this.props.close();
    }

    cancel() {
        if (this.props.getPayload) {
            this.props.getPayload({
                confirmed: false,
                selectedReservation: null,
            });
        }
        this.props.close();
    }
}
