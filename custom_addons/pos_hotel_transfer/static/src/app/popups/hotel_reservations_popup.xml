<?xml version="1.0" encoding="UTF-8"?>
<templates id="template" xml:space="preserve">
    <t t-name="pos_hotel_transfer.HotelReservationsPopup" owl="1">
        <Dialog size="'xl'" title="'Hotel Reservations'">
            <t t-set-slot="header">
                <h4 class="modal-title">
                    <i class="fa fa-hotel me-2"/>
                    Hotel Reservations
                </h4>
            </t>

            <div class="dialog-body">
                <!-- Loading State -->
                <div t-if="state.loading" class="text-center p-4">
                    <i class="fa fa-spinner fa-spin fa-2x mb-3"/>
                    <p>Loading hotel reservations...</p>
                </div>

                <!-- Error State -->
                <div t-if="state.error" class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle me-2"/>
                    <t t-esc="state.error"/>
                </div>

                <!-- Reservations List -->
                <div t-if="!state.loading and !state.error" class="reservations-container">
                    <div t-if="state.reservations.length === 0" class="text-center p-4">
                        <i class="fa fa-info-circle fa-2x mb-3 text-muted"/>
                        <p class="text-muted">No active hotel reservations found</p>
                    </div>

                    <div t-if="state.reservations.length > 0" class="reservations-list">
                        <div class="reservation-header">
                            <p class="mb-3">
                                <strong>Select a reservation to transfer the order:</strong>
                            </p>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Select</th>
                                        <th>Booking Ref</th>
                                        <th>Guest Name</th>
                                        <th>Room</th>
                                        <th>Type</th>
                                        <th>Check In</th>
                                        <th>Check Out</th>
                                        <th>Room Amount</th>
                                        <th>Due Amount</th>
                                        <th>Balance</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr t-foreach="state.reservations" t-as="reservation" t-key="reservation.id"
                                        t-att-class="state.selectedReservation?.id === reservation.id ? 'table-primary' : ''"
                                        class="reservation-row"
                                        t-on-click="() => this.selectReservation(reservation)">
                                        <td>
                                            <input type="radio"
                                                   t-att-checked="state.selectedReservation?.id === reservation.id"
                                                   t-on-change="() => this.selectReservation(reservation)"/>
                                        </td>
                                        <td>
                                            <strong t-esc="reservation.name"/>
                                        </td>
                                        <td>
                                            <i class="fa fa-user me-1"/>
                                            <t t-esc="reservation.guest_name"/>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <t t-esc="reservation.room_number"/>
                                            </span>
                                        </td>
                                        <td>
                                            <t t-esc="reservation.room_type"/>
                                        </td>
                                        <td>
                                            <t t-esc="reservation.check_in"/>
                                        </td>
                                        <td>
                                            <t t-esc="reservation.check_out"/>
                                        </td>
                                        <td>
                                            <strong class="text-success">
                                                $<t t-esc="reservation.total_amount.toFixed(2)"/>
                                            </strong>
                                        </td>
                                        <td>
                                            <strong t-att-class="reservation.due_amount > 0 ? 'text-warning' : 'text-muted'">
                                                $<t t-esc="reservation.due_amount.toFixed(2)"/>
                                            </strong>
                                        </td>
                                        <td>
                                            <strong t-att-class="reservation.balance_amount > 0 ? 'text-danger' : 'text-success'">
                                                $<t t-esc="reservation.balance_amount.toFixed(2)"/>
                                            </strong>
                                        </td>
                                        <td>
                                            <span t-att-class="'badge ' + (reservation.state === 'checked_in' ? 'bg-success' : 'bg-warning')">
                                                <t t-esc="reservation.state_label"/>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Selected Reservation Details -->
                        <div t-if="state.selectedReservation" class="selected-reservation mt-3 p-3 bg-light rounded">
                            <h6 class="mb-2">
                                <i class="fa fa-check-circle text-success me-1"/>
                                Selected Reservation Details
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Guest:</strong> <t t-esc="state.selectedReservation.guest_name"/></p>
                                    <p><strong>Room:</strong> <t t-esc="state.selectedReservation.room_number"/> (<t t-esc="state.selectedReservation.room_type"/>)</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Guests:</strong> <t t-esc="state.selectedReservation.adults"/> Adults, <t t-esc="state.selectedReservation.children"/> Children</p>
                                    <p><strong>Room Amount:</strong> $<t t-esc="state.selectedReservation.total_amount.toFixed(2)"/></p>
                                    <p><strong>Current Due:</strong> <span t-att-class="state.selectedReservation.due_amount > 0 ? 'text-warning' : 'text-muted'">$<t t-esc="state.selectedReservation.due_amount.toFixed(2)"/></span></p>
                                    <p><strong>Balance:</strong> <span t-att-class="state.selectedReservation.balance_amount > 0 ? 'text-danger' : 'text-success'">$<t t-esc="state.selectedReservation.balance_amount.toFixed(2)"/></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <t t-set-slot="footer">
                <button class="btn btn-secondary" t-on-click="cancel">
                    <i class="fa fa-times me-1"/>
                    Cancel
                </button>
                <button class="btn btn-primary"
                        t-att-disabled="!state.selectedReservation"
                        t-on-click="transferToReservation">
                    <i class="fa fa-exchange me-1"/>
                    Transfer to Hotel
                </button>
            </t>
        </Dialog>
    </t>
</templates>
