/** @odoo-module */

import { ControlButtons } from "@point_of_sale/app/screens/product_screen/control_buttons/control_buttons";
import { Component } from "@odoo/owl";
import { usePos } from "@point_of_sale/app/store/pos_hook";
import { useService } from "@web/core/utils/hooks";
import { HotelReservationsPopup } from "@pos_hotel_transfer/app/popups/hotel_reservations_popup";

export class TransferButton extends Component {
    static template = "pos_hotel_transfer.TransferButton";

    setup() {
        this.pos = usePos();
        this.dialog = useService("dialog");
        console.log('Transfer to Hotel button component setup!');
    }

    async onClick() {
        console.log('Transfer to Hotel button clicked!');

        // Check if there's an active order with items
        const currentOrder = this.pos.get_order();
        if (!currentOrder || currentOrder.get_orderlines().length === 0) {
            this.env.services.notification.add(
                "Please add items to the order before transferring to hotel",
                { type: "warning" }
            );
            return;
        }

        // Open the hotel reservations dialog
        try {
            const result = await new Promise((resolve) => {
                this.dialog.add(HotelReservationsPopup, {
                    getPayload: resolve,
                });
            });

            if (result?.confirmed && result?.selectedReservation) {
                console.log('Transfer confirmed for reservation:', result.selectedReservation);
                // Additional transfer logic can be added here if needed
            }
        } catch (error) {
            console.error('Error opening hotel reservations dialog:', error);
            this.env.services.notification.add(
                "Failed to open hotel reservations",
                { type: "danger" }
            );
        }
    }
}

// Register the TransferButton component
ControlButtons.components = { ...ControlButtons.components, TransferButton };
