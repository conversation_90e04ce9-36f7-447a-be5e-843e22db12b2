#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create hotel data programmatically
Run this in Odoo shell to create sample hotel data
"""

def create_hotel_data(env):
    """Create sample hotel data"""
    
    # Create Hotel Amenities
    amenity_wifi = env['hotel.amenity'].create({
        'name': 'WiFi',
        'description': 'Free wireless internet'
    })
    
    amenity_ac = env['hotel.amenity'].create({
        'name': 'Air Conditioning', 
        'description': 'Climate control'
    })
    
    # Create Hotel Services
    service_breakfast = env['hotel.service'].create({
        'name': 'Breakfast',
        'category': 'food',
        'price': 25.00,
        'description': 'Continental breakfast'
    })
    
    service_laundry = env['hotel.service'].create({
        'name': 'Laundry Service',
        'category': 'service', 
        'price': 15.00,
        'description': 'Same day laundry'
    })
    
    # Create Hotel Guests
    guest_john = env['hotel.guest'].create({
        'name': '<PERSON>',
        'email': '<EMAIL>',
        'phone': '******-0123',
        'nationality': 'American',
        'id_type': 'passport',
        'id_number': 'P123456789',
        'address': '123 Main St, New York, NY 10001'
    })
    
    guest_sarah = env['hotel.guest'].create({
        'name': 'Sarah <PERSON>',
        'email': '<EMAIL>', 
        'phone': '******-0456',
        'nationality': 'Canadian',
        'id_type': 'national_id',
        'id_number': 'ID987654321',
        'address': '456 Oak Ave, Toronto, ON M5V 3A8'
    })
    
    guest_michael = env['hotel.guest'].create({
        'name': 'Michael Brown',
        'email': '<EMAIL>',
        'phone': '******-0789', 
        'nationality': 'British',
        'id_type': 'passport',
        'id_number': 'P555666777',
        'address': '789 Queen St, London, UK SW1A 1AA'
    })
    
    # Create Hotel Rooms
    room_101 = env['hotel.room'].create({
        'name': '101',
        'room_type': 'single',
        'floor': 1,
        'capacity': 1,
        'price_per_night': 120.00,
        'state': 'available'
    })
    
    room_201 = env['hotel.room'].create({
        'name': '201', 
        'room_type': 'double',
        'floor': 2,
        'capacity': 2,
        'price_per_night': 180.00,
        'state': 'available'
    })
    
    room_301 = env['hotel.room'].create({
        'name': '301',
        'room_type': 'suite',
        'floor': 3,
        'capacity': 4,
        'price_per_night': 350.00,
        'state': 'available'
    })
    
    # Create Room Bookings
    from datetime import datetime, timedelta
    
    booking1 = env['room.booking'].create({
        'guest_id': guest_john.id,
        'room_id': room_101.id,
        'check_in': (datetime.now() - timedelta(days=1)).date(),
        'check_out': (datetime.now() + timedelta(days=2)).date(),
        'state': 'checked_in',
        'adults': 1,
        'children': 0
    })
    
    booking2 = env['room.booking'].create({
        'guest_id': guest_sarah.id,
        'room_id': room_201.id,
        'check_in': datetime.now().date(),
        'check_out': (datetime.now() + timedelta(days=3)).date(),
        'state': 'confirmed',
        'adults': 2,
        'children': 1
    })
    
    booking3 = env['room.booking'].create({
        'guest_id': guest_michael.id,
        'room_id': room_301.id,
        'check_in': (datetime.now() + timedelta(days=1)).date(),
        'check_out': (datetime.now() + timedelta(days=5)).date(),
        'state': 'confirmed',
        'adults': 2,
        'children': 2
    })
    
    # Update room states
    room_101.write({'state': 'occupied'})
    room_201.write({'state': 'occupied'})
    room_301.write({'state': 'occupied'})
    
    print("Hotel data created successfully!")
    print(f"Created {len([guest_john, guest_sarah, guest_michael])} guests")
    print(f"Created {len([room_101, room_201, room_301])} rooms")
    print(f"Created {len([booking1, booking2, booking3])} bookings")
    
    return {
        'guests': [guest_john, guest_sarah, guest_michael],
        'rooms': [room_101, room_201, room_301], 
        'bookings': [booking1, booking2, booking3]
    }

# To run this script in Odoo shell:
# python3 odoo-bin shell -c temp_odoo.conf -d admin
# >>> exec(open('custom_addons/pos_hotel_transfer/create_hotel_data.py').read())
# >>> create_hotel_data(env)
