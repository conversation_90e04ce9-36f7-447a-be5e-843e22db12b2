/** @odoo-module */

import { FormController } from "@web/views/form/form_controller";
import { patch } from "@web/core/utils/patch";

// Patch FormController to fix positioning issues
patch(FormController.prototype, {
    setup() {
        super.setup();
        
        // Fix form positioning after setup
        this.env.bus.addEventListener("DOM_UPDATED", () => {
            this.fixFormPositioning();
        });
    },

    fixFormPositioning() {
        // Ensure form is properly positioned
        const formView = document.querySelector('.o_form_view');
        if (formView) {
            formView.style.position = 'relative';
            formView.style.zIndex = '999';
            formView.style.background = 'white';
            formView.style.width = '100%';
            formView.style.minHeight = '100vh';
        }

        // Fix form sheet
        const formSheet = document.querySelector('.o_form_sheet');
        if (formSheet) {
            formSheet.style.position = 'relative';
            formSheet.style.zIndex = '1000';
            formSheet.style.background = 'white';
            formSheet.style.margin = '20px';
            formSheet.style.padding = '20px';
        }

        // Fix any overlapping elements
        const chatter = document.querySelector('.oe_chatter');
        if (chatter) {
            chatter.style.position = 'relative';
            chatter.style.zIndex = '5';
            chatter.style.marginTop = '30px';
            chatter.style.clear = 'both';
        }
    },

    async onWillStart() {
        await super.onWillStart();
        // Apply fixes when form starts
        setTimeout(() => {
            this.fixFormPositioning();
        }, 100);
    },

    async onMounted() {
        await super.onMounted();
        // Apply fixes when form is mounted
        this.fixFormPositioning();
        
        // Apply fixes periodically to handle dynamic content
        setInterval(() => {
            this.fixFormPositioning();
        }, 1000);
    }
});
