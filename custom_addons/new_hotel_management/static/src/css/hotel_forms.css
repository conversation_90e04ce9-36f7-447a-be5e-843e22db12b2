/* Hotel Management Form Fixes */

/* Fix main content area z-index issues */
.o_content {
    position: relative !important;
    z-index: 1 !important;
    background: white !important;
}

/* Fix form view positioning */
.o_form_view {
    position: relative !important;
    z-index: 10 !important;
    background: white !important;
    min-height: 100vh !important;
    width: 100% !important;
}

/* Fix form sheet */
.o_form_view .o_form_sheet {
    position: relative !important;
    z-index: 15 !important;
    background: white !important;
    margin: 20px !important;
    padding: 20px !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    border-radius: 8px !important;
}

/* Fix form header */
.o_form_view .o_form_statusbar {
    position: relative !important;
    z-index: 20 !important;
    background: white !important;
}

/* Fix chatter positioning */
.o_form_view .oe_chatter {
    position: relative !important;
    z-index: 5 !important;
    margin-top: 30px !important;
    clear: both !important;
    background: white !important;
}

/* Fix input fields and widgets */
.o_form_view .o_field_widget {
    position: relative !important;
    z-index: 25 !important;
    background: white !important;
}

/* Fix buttons */
.o_form_view .btn {
    position: relative !important;
    z-index: 30 !important;
}

/* Fix dropdowns and selects */
.o_form_view .o_field_many2one,
.o_form_view .o_field_selection {
    position: relative !important;
    z-index: 35 !important;
}

/* Fix modal dialogs */
.modal {
    z-index: 1050 !important;
}

.modal-dialog {
    z-index: 1055 !important;
}

/* Fix any overlay issues */
.o_web_client {
    position: relative !important;
}

/* Fix main menu and navigation */
.o_main_navbar {
    z-index: 1000 !important;
}

/* Fix action manager */
.o_action_manager {
    position: relative !important;
    z-index: 5 !important;
    background: white !important;
}

/* Fix control panel */
.o_control_panel {
    position: relative !important;
    z-index: 15 !important;
    background: white !important;
}

/* Ensure proper stacking context */
.o_form_view .o_group {
    position: relative !important;
    z-index: 20 !important;
}

.o_form_view .o_notebook {
    position: relative !important;
    z-index: 20 !important;
}

/* Fix any floating elements */
.o_form_view .float-right,
.o_form_view .float-left {
    position: relative !important;
    z-index: 25 !important;
}

/* Force form to be on top */
body.o_web_client .o_form_view {
    position: relative !important;
    z-index: 999 !important;
}
