# -*- coding: utf-8 -*-
{
    'name': 'Hotel Management',
    'version': '1.0',
    'category': 'Industries',
    'summary': 'Basic Hotel Room Management and Booking System',
    'description': """
        Simple Hotel Management System with:
        * Room Management
        * Guest Registration
        * Room Booking
    """,
    'depends': ['base', 'mail'],
    'data': [
        'security/hotel_security.xml',
        'security/ir.model.access.csv',
        'data/sequence_data.xml',
        'views/hotel_room_views.xml',
        'views/room_booking_views.xml',
        'views/hotel_guest_views.xml',
        'views/hotel_amenity_views.xml',
        'views/hotel_service_views.xml',
        'views/menu_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'new_hotel_management/static/src/css/hotel_forms.css',
            'new_hotel_management/static/src/js/form_fixes.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
