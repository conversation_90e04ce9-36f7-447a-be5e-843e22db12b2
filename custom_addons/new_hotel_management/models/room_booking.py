# -*- coding: utf-8 -*-
from odoo import api, fields, models, _
from odoo.exceptions import ValidationError

class RoomBooking(models.Model):
    _name = 'room.booking'
    _description = 'Room Booking'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Booking Reference', required=True, copy=False,
                      readonly=True, default=lambda self: _('New'))
    guest_id = fields.Many2one('hotel.guest', string='Guest', required=True,
                              tracking=True)
    room_id = fields.Many2one('hotel.room', string='Room', required=True,
                             tracking=True, domain=[('state', '=', 'available')])
    check_in = fields.Date(string='Check In', required=True, tracking=True)
    check_out = fields.Date(string='Check Out', required=True, tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('confirmed', 'Confirmed'),
        ('checked_in', 'Checked In'),
        ('checked_out', 'Checked Out'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', tracking=True)

    adults = fields.Integer(string='Adults', default=1)
    children = fields.Integer(string='Children', default=0)
    note = fields.Text(string='Notes')
    service_ids = fields.Many2many('hotel.service', string='Additional Services')
    total_nights = fields.Integer(string='Total Nights', compute='_compute_total_nights',
                                store=True)
    price_per_night = fields.Float(related='room_id.price_per_night',
                                  string='Price per Night')
    total_amount = fields.Float(string='Total Amount', compute='_compute_total_amount',
                              store=True)
    due_amount = fields.Float(string='Due Amount', default=0.0,
                             help="Outstanding amount from POS transfers")
    paid_amount = fields.Float(string='Paid Amount', default=0.0,
                              help="Amount already paid")
    balance_amount = fields.Float(string='Balance', compute='_compute_balance_amount',
                                 store=True, help="Total amount + Due amount - Paid amount")

    @api.model
    def create(self, vals):
        if vals.get('name', _('New')) == _('New'):
            vals['name'] = self.env['ir.sequence'].next_by_code('room.booking') or _('New')
        return super(RoomBooking, self).create(vals)

    @api.depends('check_in', 'check_out')
    def _compute_total_nights(self):
        for booking in self:
            if booking.check_in and booking.check_out:
                delta = booking.check_out - booking.check_in
                booking.total_nights = delta.days

    @api.depends('total_nights', 'price_per_night')
    def _compute_total_amount(self):
        for booking in self:
            booking.total_amount = booking.total_nights * booking.price_per_night

    @api.depends('total_amount', 'due_amount', 'paid_amount')
    def _compute_balance_amount(self):
        for booking in self:
            booking.balance_amount = booking.total_amount + booking.due_amount - booking.paid_amount

    def add_pos_transfer(self, amount, order_ref=None):
        """Add POS transfer amount to due amount"""
        self.ensure_one()
        self.due_amount += amount

        # Log the transfer in chatter
        message = f"POS Order transferred: ${amount:.2f}"
        if order_ref:
            message += f" (Order: {order_ref})"
        self.message_post(body=message)

        return True

    def action_confirm(self):
        for booking in self:
            if booking.state == 'draft':
                booking.state = 'confirmed'
                # Update room state
                booking.room_id.write({
                    'state': 'occupied',
                    'current_booking_id': booking.id
                })

    def action_check_in(self):
        for booking in self:
            if booking.state == 'confirmed':
                booking.state = 'checked_in'

    def action_check_out(self):
        for booking in self:
            if booking.state == 'checked_in':
                booking.state = 'checked_out'
                # Free up the room
                booking.room_id.write({
                    'state': 'available',
                    'current_booking_id': False
                })

    def action_cancel(self):
        for booking in self:
            if booking.state in ['draft', 'confirmed']:
                booking.state = 'cancelled'
                # Free up the room if it was confirmed
                if booking.state == 'confirmed':
                    booking.room_id.write({
                        'state': 'available',
                        'current_booking_id': False
                    })
                booking.room_id.state = 'occupied'

    def action_cancel(self):
        for booking in self:
            if booking.state == 'draft':
                booking.state = 'cancelled'
