<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_room_booking_list" model="ir.ui.view">
        <field name="name">room.booking.list</field>
        <field name="model">room.booking</field>
        <field name="arch" type="xml">
            <list string="Bookings" decoration-info="state == 'draft'"
                  decoration-success="state == 'checked_in'"
                  decoration-muted="state == 'checked_out'"
                  decoration-danger="state == 'cancelled'">
                <field name="name"/>
                <field name="guest_id"/>
                <field name="room_id"/>
                <field name="check_in"/>
                <field name="check_out"/>
                <field name="total_nights"/>
                <field name="total_amount"/>
                <field name="due_amount" decoration-warning="due_amount > 0"/>
                <field name="balance_amount" decoration-danger="balance_amount > 0"/>
                <field name="state"/>
            </list>
        </field>
    </record>

    <!-- Simple Form View -->
    <record id="view_room_booking_form_simple" model="ir.ui.view">
        <field name="name">room.booking.form.simple</field>
        <field name="model">room.booking</field>
        <field name="arch" type="xml">
            <form string="Booking">
                <header>
                    <button name="action_confirm" string="Confirm" type="object"
                            class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_check_in" string="Check In" type="object"
                            class="oe_highlight" invisible="state != 'confirmed'"/>
                    <button name="action_check_out" string="Check Out" type="object"
                            class="oe_highlight" invisible="state != 'checked_in'"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            invisible="state not in ('draft', 'confirmed')"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,confirmed,checked_in,checked_out"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Guest Information">
                            <field name="guest_id" required="1"/>
                            <field name="adults"/>
                            <field name="children"/>
                        </group>
                        <group string="Room Information">
                            <field name="room_id" required="1"/>
                            <field name="price_per_night"/>
                        </group>
                    </group>
                    <group>
                        <group string="Dates">
                            <field name="check_in" required="1"/>
                            <field name="check_out" required="1"/>
                            <field name="total_nights"/>
                        </group>
                        <group string="Financial Summary">
                            <field name="total_amount" class="oe_subtotal_footer_separator"/>
                            <field name="due_amount" class="text-warning"/>
                            <field name="paid_amount" class="text-success"/>
                            <field name="balance_amount" class="oe_subtotal_footer_separator text-danger"/>
                        </group>
                    </group>
                    <group string="Notes">
                        <field name="note" nolabel="1"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Full Form View -->
    <record id="view_room_booking_form" model="ir.ui.view">
        <field name="name">room.booking.form</field>
        <field name="model">room.booking</field>
        <field name="arch" type="xml">
            <form string="Booking">
                <header>
                    <button name="action_confirm" string="Confirm" type="object"
                            class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_check_in" string="Check In" type="object"
                            class="oe_highlight" invisible="state != 'confirmed'"/>
                    <button name="action_check_out" string="Check Out" type="object"
                            class="oe_highlight" invisible="state != 'checked_in'"/>
                    <button name="action_cancel" string="Cancel" type="object"
                            invisible="state not in ('draft', 'confirmed')"/>
                    <field name="state" widget="statusbar"
                           statusbar_visible="draft,confirmed,checked_in,checked_out"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="guest_id"/>
                            <field name="room_id"/>
                            <field name="adults"/>
                            <field name="children"/>
                        </group>
                        <group>
                            <field name="check_in"/>
                            <field name="check_out"/>
                            <field name="total_nights"/>
                            <field name="price_per_night"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Additional Services">
                            <field name="service_ids">
                                <list>
                                    <field name="name"/>
                                    <field name="category"/>
                                    <field name="price"/>
                                </list>
                            </field>
                        </page>
                        <page string="Notes">
                            <field name="note"/>
                        </page>
                    </notebook>
                    <group class="oe_subtotal_footer">
                        <field name="total_amount" class="oe_subtotal_footer_separator"/>
                        <field name="due_amount" class="text-warning"/>
                        <field name="paid_amount" class="text-success"/>
                        <field name="balance_amount" class="oe_subtotal_footer_separator text-danger"/>
                    </group>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids" groups="base.group_user"/>
                    <field name="message_ids" groups="base.group_user"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_room_booking_search" model="ir.ui.view">
        <field name="name">room.booking.search</field>
        <field name="model">room.booking</field>
        <field name="arch" type="xml">
            <search string="Bookings">
                <field name="name"/>
                <field name="guest_id"/>
                <field name="room_id"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Confirmed" name="confirmed" domain="[('state', '=', 'confirmed')]"/>
                <filter string="Checked In" name="checked_in" domain="[('state', '=', 'checked_in')]"/>
                <filter string="Checked Out" name="checked_out" domain="[('state', '=', 'checked_out')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <group expand="0" string="Group By">
                    <filter string="Guest" name="group_guest" context="{'group_by': 'guest_id'}"/>
                    <filter string="Room" name="group_room" context="{'group_by': 'room_id'}"/>
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Calendar View -->
    <record id="view_room_booking_calendar" model="ir.ui.view">
        <field name="name">room.booking.calendar</field>
        <field name="model">room.booking</field>
        <field name="arch" type="xml">
            <calendar string="Bookings" date_start="check_in" date_stop="check_out" mode="month">
                <field name="guest_id"/>
                <field name="room_id"/>
                <field name="state"/>
            </calendar>
        </field>
    </record>

    <!-- Action -->
    <record id="action_room_booking" model="ir.actions.act_window">
        <field name="name">Bookings</field>
        <field name="res_model">room.booking</field>
        <field name="view_mode">list,form,calendar</field>
        <field name="search_view_id" ref="view_room_booking_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first booking!
            </p>
        </field>
    </record>
</odoo>
