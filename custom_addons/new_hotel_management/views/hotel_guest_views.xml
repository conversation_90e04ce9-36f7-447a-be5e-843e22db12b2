<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_hotel_guest_list" model="ir.ui.view">
        <field name="name">hotel.guest.list</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <list string="Guests">
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="nationality"/>
                <field name="id_type"/>
                <field name="id_number"/>
            </list>
        </field>
    </record>

    <!-- Simple Form View -->
    <record id="view_hotel_guest_form_simple" model="ir.ui.view">
        <field name="name">hotel.guest.form.simple</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <form string="Guest">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Guest Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="email"/>
                            <field name="phone"/>
                            <field name="nationality"/>
                        </group>
                        <group>
                            <field name="id_type"/>
                            <field name="id_number"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <group>
                        <field name="address" string="Address"/>
                    </group>
                    <group>
                        <field name="note" string="Notes"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Full Form View -->
    <record id="view_hotel_guest_form" model="ir.ui.view">
        <field name="name">hotel.guest.form</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <form string="Guest">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Guest Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="email"/>
                            <field name="phone"/>
                            <field name="nationality"/>
                        </group>
                        <group>
                            <field name="id_type"/>
                            <field name="id_number"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Address">
                            <field name="address"/>
                        </page>
                        <page string="Bookings">
                            <field name="booking_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="room_id"/>
                                    <field name="check_in"/>
                                    <field name="check_out"/>
                                    <field name="state"/>
                                    <field name="total_amount"/>
                                </list>
                            </field>
                        </page>
                        <page string="Notes">
                            <field name="note"/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" groups="base.group_user"/>
                    <field name="activity_ids" groups="base.group_user"/>
                    <field name="message_ids" groups="base.group_user"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_hotel_guest_search" model="ir.ui.view">
        <field name="name">hotel.guest.search</field>
        <field name="model">hotel.guest</field>
        <field name="arch" type="xml">
            <search string="Guests">
                <field name="name"/>
                <field name="email"/>
                <field name="phone"/>
                <field name="id_number"/>
                <field name="nationality"/>
                <group expand="0" string="Group By">
                    <filter string="ID Type" name="group_id_type" context="{'group_by': 'id_type'}"/>
                    <filter string="Nationality" name="group_nationality" context="{'group_by': 'nationality'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_hotel_guest" model="ir.actions.act_window">
        <field name="name">Guests</field>
        <field name="res_model">hotel.guest</field>
        <field name="view_mode">list,form</field>
        <field name="view_id" ref="view_hotel_guest_form_simple"/>
        <field name="search_view_id" ref="view_hotel_guest_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first guest!
            </p>
        </field>
    </record>
</odoo>
